using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using ScoreboardCsvIngestor.Services;
using System.Net;
using System.Text.Json;
using System.Text;

namespace ScoreboardCsvIngestor.Functions;

public class CsvIngestorFunction
{
    private readonly ILogger<CsvIngestorFunction> _logger;
    private readonly ICsvParserService _csvParserService;
    private readonly IScoreboardDataService _dataService;

    public CsvIngestorFunction(
        ILogger<CsvIngestorFunction> logger,
        ICsvParserService csvParserService,
        IScoreboardDataService dataService)
    {
        _logger = logger;
        _csvParserService = csvParserService;
        _dataService = dataService;
    }

    [Function("ProcessCsvUpload")]
    public async Task<HttpResponseData> ProcessCsvUpload(
        [HttpTrigger(AuthorizationLevel.Function, "post", Route = "csv/upload")] HttpRequestData req)
    {
        var correlationId = Guid.NewGuid().ToString();
        _logger.LogInformation("CSV processing request started. CorrelationId: {CorrelationId}", correlationId);

        try
        {
            // Validate request
            if (req.Body == null)
            {
                _logger.LogWarning("Request body is null. CorrelationId: {CorrelationId}", correlationId);
                return await CreateErrorResponse(req, HttpStatusCode.BadRequest, "Request body is required", correlationId);
            }

            // Test database connection first
            if (!await _dataService.TestConnectionAsync())
            {
                _logger.LogError("Database connection test failed. CorrelationId: {CorrelationId}", correlationId);
                return await CreateErrorResponse(req, HttpStatusCode.ServiceUnavailable, "Database connection failed", correlationId);
            }

            // Extract CSV content from request
            _logger.LogInformation("Extracting CSV content from request. CorrelationId: {CorrelationId}", correlationId);
            var csvContent = await ExtractCsvContentAsync(req);

            if (string.IsNullOrEmpty(csvContent))
            {
                _logger.LogWarning("No CSV content found in request. CorrelationId: {CorrelationId}", correlationId);
                return await CreateErrorResponse(req, HttpStatusCode.BadRequest, "No CSV file found in request", correlationId);
            }

            // Parse CSV content
            _logger.LogInformation("Starting CSV parsing. CorrelationId: {CorrelationId}", correlationId);
            var parseResult = await _csvParserService.ParseCsvAsync(csvContent);

            if (!parseResult.IsSuccess)
            {
                _logger.LogWarning("CSV parsing failed. Errors: {Errors}. CorrelationId: {CorrelationId}", 
                    string.Join(", ", parseResult.Errors), correlationId);
                
                return await CreateErrorResponse(req, HttpStatusCode.BadRequest, 
                    "CSV parsing failed", correlationId, parseResult.Errors, parseResult.ValidationErrors);
            }

            if (parseResult.Records.Count == 0)
            {
                _logger.LogWarning("No valid records found in CSV. CorrelationId: {CorrelationId}", correlationId);
                return await CreateErrorResponse(req, HttpStatusCode.BadRequest, "No valid records found in CSV", correlationId);
            }

            // Insert records into database
            _logger.LogInformation("Starting database insertion for {RecordCount} records. CorrelationId: {CorrelationId}", 
                parseResult.Records.Count, correlationId);
            
            var insertResult = await _dataService.InsertRecordsAsync(parseResult.Records);

            if (!insertResult.IsSuccess)
            {
                _logger.LogError("Database insertion failed. Errors: {Errors}. CorrelationId: {CorrelationId}", 
                    string.Join(", ", insertResult.Errors), correlationId);
                
                return await CreateErrorResponse(req, HttpStatusCode.InternalServerError, 
                    "Database insertion failed", correlationId, insertResult.Errors);
            }

            // Create success response
            var successResponse = new
            {
                success = true,
                correlationId,
                message = "CSV processed successfully",
                data = new
                {
                    totalRowsProcessed = parseResult.TotalRowsProcessed,
                    validRowsCount = parseResult.ValidRowsCount,
                    invalidRowsCount = parseResult.InvalidRowsCount,
                    recordsInserted = insertResult.RecordsInserted,
                    processingTimeMs = insertResult.ProcessingTime.TotalMilliseconds
                },
                timestamp = DateTime.UtcNow
            };

            _logger.LogInformation("CSV processing completed successfully. Records inserted: {RecordsInserted}. CorrelationId: {CorrelationId}", 
                insertResult.RecordsInserted, correlationId);

            var response = req.CreateResponse(HttpStatusCode.OK);
            response.Headers.Add("Content-Type", "application/json");
            await response.WriteStringAsync(JsonSerializer.Serialize(successResponse, new JsonSerializerOptions 
            { 
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase 
            }));

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error during CSV processing. CorrelationId: {CorrelationId}", correlationId);
            return await CreateErrorResponse(req, HttpStatusCode.InternalServerError, 
                "An unexpected error occurred", correlationId, new List<string> { ex.Message });
        }
    }

    [Function("HealthCheck")]
    public async Task<HttpResponseData> HealthCheck(
        [HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "health")] HttpRequestData req)
    {
        _logger.LogInformation("Health check requested");

        try
        {
            var dbConnectionOk = await _dataService.TestConnectionAsync();
            
            var healthStatus = new
            {
                status = dbConnectionOk ? "healthy" : "unhealthy",
                timestamp = DateTime.UtcNow,
                checks = new
                {
                    database = dbConnectionOk ? "ok" : "failed"
                }
            };

            var statusCode = dbConnectionOk ? HttpStatusCode.OK : HttpStatusCode.ServiceUnavailable;
            var response = req.CreateResponse(statusCode);
            response.Headers.Add("Content-Type", "application/json");
            await response.WriteStringAsync(JsonSerializer.Serialize(healthStatus, new JsonSerializerOptions 
            { 
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase 
            }));

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during health check");
            return await CreateErrorResponse(req, HttpStatusCode.InternalServerError, "Health check failed", Guid.NewGuid().ToString());
        }
    }

    private async Task<HttpResponseData> CreateErrorResponse(
        HttpRequestData req, 
        HttpStatusCode statusCode, 
        string message, 
        string correlationId,
        List<string>? errors = null,
        List<ValidationError>? validationErrors = null)
    {
        var errorResponse = new
        {
            success = false,
            correlationId,
            message,
            errors = errors ?? new List<string>(),
            validationErrors = validationErrors?.Select(ve => new
            {
                rowNumber = ve.RowNumber,
                fieldName = ve.FieldName,
                errorMessage = ve.ErrorMessage,
                invalidValue = ve.InvalidValue
            }).Cast<object>().ToList() ?? new List<object>(),
            timestamp = DateTime.UtcNow
        };

        var response = req.CreateResponse(statusCode);
        response.Headers.Add("Content-Type", "application/json");
        await response.WriteStringAsync(JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions 
        { 
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase 
        }));

        return response;
    }

    private async Task<string?> ExtractCsvContentAsync(HttpRequestData req)
    {
        try
        {
            var contentType = req.Headers.GetValues("Content-Type").FirstOrDefault();
            _logger.LogInformation("Request Content-Type: {ContentType}", contentType ?? "null");

            // Read the entire request body first
            using var reader = new StreamReader(req.Body, Encoding.UTF8);
            var content = await reader.ReadToEndAsync();

            _logger.LogInformation("Request body length: {Length}", content.Length);
            _logger.LogInformation("First 200 chars of request body: {Content}",
                content.Length > 200 ? content.Substring(0, 200) : content);

            // Check if content looks like multipart data
            if (content.Contains("Content-Disposition: form-data") ||
                (contentType != null && contentType.StartsWith("multipart/form-data")))
            {
                _logger.LogInformation("Detected multipart form data, extracting CSV content");
                return ExtractCsvFromMultipartString(content);
            }

            // Check if it's plain text/csv
            if (contentType != null && (contentType.Contains("text/csv") || contentType.Contains("text/plain")))
            {
                _logger.LogInformation("Detected plain text/CSV content");
                return content;
            }

            // Default: return content as-is
            _logger.LogInformation("Using content as-is");
            return content;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting CSV content from request");
            return null;
        }
    }



    private string? ExtractCsvFromMultipartString(string multipartContent)
    {
        try
        {
            _logger.LogInformation("Starting multipart parsing, content length: {Length}", multipartContent.Length);

            // Find the boundary
            var lines = multipartContent.Split(new[] { "\r\n", "\n" }, StringSplitOptions.None);
            _logger.LogInformation("Split into {LineCount} lines", lines.Length);

            bool foundFileSection = false;
            bool foundEmptyLine = false;
            var csvLines = new List<string>();

            for (int i = 0; i < lines.Length; i++)
            {
                var line = lines[i];
                _logger.LogDebug("Line {Index}: '{Line}'", i, line.Length > 100 ? line.Substring(0, 100) + "..." : line);

                // Skip boundary lines
                if (line.StartsWith("--"))
                {
                    _logger.LogInformation("Found boundary line at index {Index}: {Line}", i, line);
                    if (foundFileSection && csvLines.Any())
                    {
                        _logger.LogInformation("End of file section, collected {Count} CSV lines", csvLines.Count);
                        break;
                    }
                    foundFileSection = false;
                    foundEmptyLine = false;
                    continue;
                }

                // Look for file content disposition
                if (line.Contains("Content-Disposition: form-data") && line.Contains("filename="))
                {
                    _logger.LogInformation("Found file content disposition at line {Index}", i);
                    foundFileSection = true;
                    continue;
                }

                // Skip content-type line
                if (line.StartsWith("Content-Type:"))
                {
                    _logger.LogInformation("Skipping Content-Type line at index {Index}", i);
                    continue;
                }

                // Empty line indicates start of actual content
                if (foundFileSection && string.IsNullOrEmpty(line))
                {
                    _logger.LogInformation("Found empty line indicating start of content at index {Index}", i);
                    foundEmptyLine = true;
                    continue;
                }

                // Collect CSV content lines
                if (foundFileSection && foundEmptyLine)
                {
                    _logger.LogDebug("Adding CSV line {Index}: '{Line}'", csvLines.Count, line);
                    csvLines.Add(line);
                }
            }

            _logger.LogInformation("Extracted {Count} CSV lines", csvLines.Count);

            if (csvLines.Any())
            {
                // Remove any trailing empty lines
                while (csvLines.Count > 0 && string.IsNullOrEmpty(csvLines.Last()))
                {
                    csvLines.RemoveAt(csvLines.Count - 1);
                }

                var result = string.Join("\r\n", csvLines);
                _logger.LogInformation("Final CSV content length: {Length}", result.Length);
                _logger.LogInformation("First 200 chars of extracted CSV: {Content}",
                    result.Length > 200 ? result.Substring(0, 200) : result);

                return result;
            }

            _logger.LogWarning("No CSV lines extracted from multipart content");
            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing multipart content");
            return null;
        }
    }
}
