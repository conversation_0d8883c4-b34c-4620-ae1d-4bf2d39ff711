

using Microsoft.Extensions.Configuration;

namespace ScoreboardCsvIngestor.Configuration;

public class AppSettings
{
    public string SqlConnectionString { get; set; } = string.Empty;
    public int MaxCsvRows { get; set; } = 500;
    public bool EnableDetailedLogging { get; set; } = false;
    public string ApplicationInsightsConnectionString { get; set; } = string.Empty;
    public bool UseManagedIdentity { get; set; } = false;
}

public static class ConfigurationExtensions
{
    public static AppSettings GetAppSettings(this IConfiguration configuration)
    {
        return new AppSettings
        {
            // In Azure Functions, connection strings are stored in the Values section
            SqlConnectionString = configuration["SqlConnectionString"] ?? string.Empty,
            MaxCsvRows = configuration.GetValue<int>("MaxCsvRows", 500),
            EnableDetailedLogging = configuration.GetValue<bool>("EnableDetailedLogging", false),
            ApplicationInsightsConnectionString = configuration["APPLICATIONINSIGHTS_CONNECTION_STRING"] ?? string.Empty,
            UseManagedIdentity = configuration.GetValue<bool>("UseManagedIdentity", false)
        };
    }
}
